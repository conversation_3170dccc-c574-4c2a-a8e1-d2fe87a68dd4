import React, { useEffect, useRef, useState } from 'react';
import * as PIXI from 'pixi.js';
import NumberFlow from '@number-flow/react';

const Lmh2 = () => {
    const containerRef = useRef(null);
    const pixiAppRef = useRef(null);
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

    // 🎬 动画状态
    const [isCanvasVisible, setIsCanvasVisible] = useState(false);
    const [isDataFlowing, setIsDataFlowing] = useState(false);

    // 📊 数据状态
    const [flowMetrics, setFlowMetrics] = useState({
        inletFlow: 0,
        inletPressure: 0,
        outletFlow: 0,
        outletPressure: 0,
        inletTotalFlow: 0,
        outletTotalFlow: 0,
    });

    // 🎯 视图模式状态
    const [viewMode, setViewMode] = useState('2D');
    const [buttonAnimStates, setButtonAnimStates] = useState({
        '2D': { isVisible: false },
        '2.5D': { isVisible: false },
        '3D': { isVisible: false }
    });

    // 🎬 初始化 PixiJS 应用
    useEffect(() => {
        if (!containerRef.current || pixiAppRef.current) return;

        const initPixiApp = async () => {
            const app = new PIXI.Application();
            await app.init({
                width: dimensions.width || 800,
                height: dimensions.height || 400,
                backgroundColor: 0xf9fafb,
                antialias: true,
                resolution: window.devicePixelRatio || 1,
                autoDensity: true
            });

            pixiAppRef.current = app;
            containerRef.current.appendChild(app.canvas);

            // 创建主容器
            const mainContainer = new PIXI.Container();
            app.stage.addChild(mainContainer);

            // 创建简单的演示图形
            const graphics = new PIXI.Graphics();

            // 绘制管道
            graphics.stroke({ width: 4, color: 0x23C26D });
            graphics.moveTo(50, 200);
            graphics.lineTo(750, 200);

            // 绘制设备
            for (let i = 0; i < 6; i++) {
                const x = 100 + i * 120;
                const y = 150;

                // 设备外框
                graphics.stroke({ width: 2, color: 0xd2d1da });
                graphics.fill(0x141414);
                graphics.roundRect(x, y, 70, 70, 10);
                graphics.fill();
                graphics.stroke();

                // 风扇中心
                graphics.fill(0x23C26D);
                graphics.circle(x + 35, y + 35, 15);
                graphics.fill();
            }

            mainContainer.addChild(graphics);

            // 添加文本
            const titleText = new PIXI.Text({
                text: 'PixiJS 高性能泵房工况图',
                style: {
                    fontFamily: 'Arial',
                    fontSize: 24,
                    fill: 0x333333,
                    align: 'center'
                }
            });
            titleText.x = (dimensions.width || 800) / 2 - titleText.width / 2;
            titleText.y = 50;
            mainContainer.addChild(titleText);

            // 简单的动画
            let rotation = 0;
            app.ticker.add(() => {
                rotation += 0.1;
                // 这里可以添加风扇旋转动画
            });
        };

        initPixiApp();

        return () => {
            if (pixiAppRef.current) {
                pixiAppRef.current.destroy(true);
                pixiAppRef.current = null;
            }
        };
    }, [dimensions]);

    // 📏 监听容器尺寸变化
    useEffect(() => {
        if (!containerRef.current) return;

        const updateDimensions = () => {
            const containerRect = containerRef.current.getBoundingClientRect();
            const width = containerRect.width || 800;
            const height = containerRect.height || 400;
            setDimensions({ width, height });
        };

        updateDimensions();
        window.addEventListener('resize', updateDimensions);

        const resizeObserver = new ResizeObserver(updateDimensions);
        resizeObserver.observe(containerRef.current);

        return () => {
            window.removeEventListener('resize', updateDimensions);
            resizeObserver.disconnect();
        };
    }, []);
    


    // 📏 监听容器尺寸变化
    useEffect(() => {
        if (!containerRef.current) return;

        const updateDimensions = () => {
            const containerRect = containerRef.current.getBoundingClientRect();
            const width = containerRect.width || 400;
            const height = containerRect.height || 300;
            setDimensions({ width, height });
        };

        updateDimensions();
        window.addEventListener('resize', updateDimensions);
        
        const resizeObserver = new ResizeObserver(updateDimensions);
        resizeObserver.observe(containerRef.current);

        return () => {
            window.removeEventListener('resize', updateDimensions);
            resizeObserver.disconnect();
        };
    }, []);

    // 🎬 启动动画序列
    useEffect(() => {
        const timer = setTimeout(() => {
            setIsCanvasVisible(true);
        }, 1000);
        return () => clearTimeout(timer);
    }, []);

    useEffect(() => {
        if (isCanvasVisible) {
            const dataDelay = setTimeout(() => {
                setIsDataFlowing(true);
            }, 1000);
            return () => clearTimeout(dataDelay);
        }
    }, [isCanvasVisible]);

    // 📊 模拟数据更新
    useEffect(() => {
        if (isDataFlowing) {
            setFlowMetrics({
                inletFlow: parseFloat((120 + Math.random() * 10).toFixed(2)),
                inletPressure: parseFloat((0.5 + Math.random() * 0.1).toFixed(2)),
                outletFlow: parseFloat((105 + Math.random() * 10).toFixed(2)),
                outletPressure: parseFloat((0.85 + Math.random() * 0.1).toFixed(2)),
                inletTotalFlow: 1234567.8,
                outletTotalFlow: 1102102.1,
            });

            const interval = setInterval(() => {
                setFlowMetrics(prevMetrics => ({
                    inletFlow: parseFloat((120 + Math.random() * 10).toFixed(2)),
                    inletPressure: parseFloat((0.5 + Math.random() * 0.1).toFixed(2)),
                    outletFlow: parseFloat((105 + Math.random() * 10).toFixed(2)),
                    outletPressure: parseFloat((0.85 + Math.random() * 0.1).toFixed(2)),
                    inletTotalFlow: prevMetrics.inletTotalFlow + parseFloat(((120 + Math.random() * 10) / 1800).toFixed(2)),
                    outletTotalFlow: prevMetrics.outletTotalFlow + parseFloat(((105 + Math.random() * 10) / 1800).toFixed(2)),
                }));
            }, 2000);
            return () => clearInterval(interval);
        }
    }, [isDataFlowing]);

    // 🎬 按钮入场动画
    useEffect(() => {
        const buttons = ['2D', '2.5D', '3D'];
        const timers = [];

        buttons.forEach((buttonKey) => {
            const randomDelay = 0.5 + Math.random() * 0.5;
            const timer = setTimeout(() => {
                setButtonAnimStates(prev => ({ ...prev, [buttonKey]: { isVisible: true } }));
            }, randomDelay * 1000);
            timers.push(timer);
        });

        return () => {
            timers.forEach(timer => clearTimeout(timer));
        };
    }, []);

    return (
        <div 
            ref={containerRef} 
            className="w-full h-full bg-gray-50 rounded-lg overflow-hidden relative flex flex-col"
            style={{ width: '100%', height: '100%' }}
        >
            {/* 💫 按钮闪烁入场动画样式 */}
            <style dangerouslySetInnerHTML={{
                __html: `
                @keyframes buttonEntranceFlash {
                    0% { opacity: 0; }
                    15% { opacity: 0.8; }
                    30% { opacity: 0.2; }
                    45% { opacity: 0.7; }
                    60% { opacity: 0.1; }
                    75% { opacity: 1; }
                    90% { opacity: 0.2; }
                    100% { opacity: 1; }
                }
                .button-animate-flash {
                    opacity: 0;
                    animation-name: buttonEntranceFlash;
                    animation-duration: 0.3s;
                    animation-timing-function: steps(7, end);
                    animation-fill-mode: forwards;
                    pointer-events: auto;
                }
                `
            }} />
            
            {/* 📊 标题栏 - 简化版本 */}
            <div className="h-[30px] w-full flex items-center justify-between relative flex-shrink-0">
                <div className="flex items-center ml-[25px]">
                    <div className="bg-[#26774D] rounded-[3px] px-[5px] h-full flex items-center">
                        <span className="text-[14px] font-['DingTalkJinBuTi',sans-serif] text-white whitespace-nowrap select-none">
                            {/* {titleList[0].name} */}
                            titleList
                        </span>
                    </div>
                </div>
                
                <div className="flex items-center space-x-[10px] mr-[0]">
                    <div className="flex items-center space-x-[5px]">
                        <div className="w-[8px] h-[8px] rounded-full bg-[#23C26D] animate-pulse"></div>
                        <span className="text-[10px] text-[#23C26D] font-['DingTalkJinBuTi']">运行中</span>
                    </div>
                </div>
                
                <div 
                    className="absolute bottom-[0px] h-[2px] bg-gradient-to-r from-transparent via-[#2894B7] to-transparent"
                    style={{ width: '100%', opacity: 0.8 }}
                ></div>
            </div>
            
            {/* 🎮 主要内容区域 */}
            <div className="flex-1 overflow-hidden relative">
                {/* 🎯 右上角操作按钮组 */}
                <div className="absolute top-[10px] right-[0] z-20">
                    <div className="flex space-x-[8px]">
                        {['2D', '2.5D', '3D'].map(mode => (
                            <div 
                                key={mode}
                                className={`px-[5px] rounded-[4px] flex items-center justify-center cursor-pointer ${
                                    buttonAnimStates[mode].isVisible ? 'button-animate-flash' : 'opacity-0'
                                } ${
                                    viewMode === mode 
                                        ? 'bg-[#23A760] shadow-[0_0_15px_rgba(35,167,96,0.5)]' 
                                        : 'bg-[#333135] hover:bg-[#444247]'
                                }`}
                                title={`${mode}视图`}
                                onClick={() => setViewMode(mode)}
                            >
                                <span className={`text-[12px] font-medium ${
                                    viewMode === mode ? 'text-black' : 'text-white'
                                }`}>{mode}</span>
                            </div>
                        ))}
                    </div>
                </div>
                
                {/* 🎨 PixiJS 画布 */}
                <div
                    ref={containerRef}
                    className={`w-full h-full flex items-center justify-center relative ${isCanvasVisible ? 'button-animate-flash' : 'opacity-0'}`}
                >
                    {/* PixiJS 应用将在这里渲染 */}
                </div>

                {/* 📊 数据显示覆盖层 */}
                {isDataFlowing && (
                    <NumberFlowOverlay
                        flowMetrics={flowMetrics}
                        isDataFlowing={isDataFlowing}
                        dimensions={dimensions}
                    />
                )}
            </div>
        </div>
    );
};

// 📊 数据显示覆盖层组件 - 使用 HTML 覆盖在 PixiJS 画布上
const NumberFlowOverlay = ({ flowMetrics, isDataFlowing, dimensions }) => {
    // 📍 数据显示位置配置 (基于屏幕坐标)
    const dataPositions = [
        {
            id: 'inlet-flow',
            x: 150,
            y: 100,
            value: flowMetrics.inletFlow,
            unit: 'L/s',
            label: '进口流量',
            color: '#23C26D'
        },
        {
            id: 'inlet-pressure',
            x: 300,
            y: 100,
            value: flowMetrics.inletPressure,
            unit: 'MPa',
            label: '进口压力',
            color: '#2894B7'
        },
        {
            id: 'outlet-flow',
            x: 450,
            y: 100,
            value: flowMetrics.outletFlow,
            unit: 'L/s',
            label: '出口流量',
            color: '#23C26D'
        },
        {
            id: 'outlet-pressure',
            x: 600,
            y: 100,
            value: flowMetrics.outletPressure,
            unit: 'MPa',
            label: '出口压力',
            color: '#2894B7'
        }
    ];

    if (!isDataFlowing) return null;

    return (
        <div className="absolute inset-0 pointer-events-none">
            {dataPositions.map(position => {
                return (
                    <div
                        key={position.id}
                        className="absolute transform -translate-x-1/2 -translate-y-1/2"
                        style={{
                            left: `${position.x}px`,
                            top: `${position.y}px`,
                        }}
                    >
                        {/* 📊 数据容器 */}
                        <div className="bg-black/80 rounded-[4px] px-[6px] py-[2px] backdrop-blur-sm border border-gray-600">
                            {/* 🏷️ 标签 */}
                            <div
                                className="text-[10px] text-center mb-[1px] font-['DingTalkJinBuTi']"
                                style={{ color: position.color }}
                            >
                                {position.label}
                            </div>

                            {/* 📈 数值显示 */}
                            <div className="flex items-center justify-center space-x-[2px]">
                                <NumberFlow
                                    value={position.value}
                                    format={{
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2
                                    }}
                                    className="text-white font-mono font-bold text-[10px]"
                                />
                                <span
                                    className="text-[8px] font-['DingTalkJinBuTi']"
                                    style={{ color: position.color }}
                                >
                                    {position.unit}
                                </span>
                            </div>
                        </div>
                    </div>
                );
            })}
        </div>
    );
};

export default Lmh2;
